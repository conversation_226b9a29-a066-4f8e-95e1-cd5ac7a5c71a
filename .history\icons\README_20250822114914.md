# 图标文件说明

此文件夹应包含以下图标文件：

- `icon16.png` - 16x16像素，用于扩展程序列表
- `icon32.png` - 32x32像素，用于扩展程序管理页面
- `icon48.png` - 48x48像素，用于扩展程序详情页面和通知
- `icon128.png` - 128x128像素，用于Chrome Web Store

## 图标要求

- 格式：PNG
- 背景：透明或白色
- 设计：简洁明了，能够清楚表达"监控"或"账号"的概念
- 颜色：建议使用蓝色或绿色系，与扩展程序主题保持一致

## 临时解决方案

**当前状态**: 为了避免加载错误，manifest.json中已暂时移除图标引用。扩展程序可以正常工作，只是会使用Chrome默认图标。

如果需要自定义图标，可以：
1. 创建所需尺寸的PNG图标文件
2. 将图标文件放入此文件夹
3. 在manifest.json中添加图标配置：

```json
"icons": {
  "16": "icons/icon16.png",
  "32": "icons/icon32.png",
  "48": "icons/icon48.png",
  "128": "icons/icon128.png"
},
"action": {
  "default_icon": {
    "16": "icons/icon16.png",
    "32": "icons/icon32.png",
    "48": "icons/icon48.png",
    "128": "icons/icon128.png"
  }
}
```

推荐的免费图标资源：
- Flaticon (https://www.flaticon.com/)
- Icons8 (https://icons8.com/)
- Feather Icons (https://feathericons.com/)
