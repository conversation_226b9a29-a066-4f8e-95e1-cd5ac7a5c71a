# 账号监控 Chrome扩展程序

一个用于实时监控pzds.com商品更新并通知用户的Chrome浏览器扩展程序。

## 功能特性

- **游戏类型选择**: 通过API获取可监控的游戏列表，支持单个游戏类型监控
- **游戏筛选**: 支持搜索功能快速找到目标游戏类型
- **实时商品监控**: 根据设置的时间间隔定时检查商品更新
- **价格过滤**: 只监控指定价格区间内的新商品
- **浏览器通知**: 发现新商品时立即发送通知，支持点击跳转到商品详情页
- **历史记录**: 保存7天内的通知历史记录

## 项目结构

```
账号监控/
├── manifest.json          # 扩展程序配置文件
├── background.js          # 后台脚本，处理API调用和监控逻辑
├── content.js            # 内容脚本，抓取商品页面数据
├── popup.html            # 弹出页面HTML
├── popup.css             # 弹出页面样式
├── popup.js              # 弹出页面脚本
├── icons/                # 图标文件夹
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md             # 项目说明文档
```

## 安装方法

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入扩展程序管理页面 (`chrome://extensions/`)
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"按钮
5. 选择项目文件夹（包含manifest.json的文件夹）
6. 扩展程序安装完成，可以在浏览器工具栏看到扩展程序图标

**注意**: 如果遇到图标相关错误，这是正常的，扩展程序功能不受影响。您可以稍后添加图标文件或忽略此警告。

## 使用方法

1. **选择游戏类型**: 点击扩展程序图标，在弹出页面中选择要监控的游戏类型
   - 使用搜索框快速筛选游戏类型
   - 只能选择一个游戏类型进行监控
2. **设置价格区间**: 输入最低价格和最高价格，只有在此区间内的商品才会触发通知
3. **选择检查间隔**: 选择监控的时间间隔（30秒、1分钟、5分钟、10分钟）
4. **开始监控**: 点击"开始监控"按钮启动实时监控
5. **查看通知**: 在通知历史区域查看最近的通知记录，点击可跳转到商品页面

## 技术实现

- **Manifest V3**: 使用最新的Chrome扩展程序规范
- **chrome.alarms API**: 实现定时监控任务
- **chrome.notifications API**: 发送浏览器通知
- **chrome.storage API**: 存储用户配置和历史记录
- **Content Scripts**: 抓取商品页面数据
- **网页抓取**: 通过DOM解析获取商品信息
- **API调用**: 使用正确的参数格式调用pzds.com游戏类型API

## 监控数据

扩展程序会抓取以下商品信息：
- 商品标题
- 价格（元）
- 商品图片
- 发布时间（审核通过时间）
- 热度
- 是否支持包赔

## 故障排除

### 游戏列表显示"暂无可用游戏"

如果遇到此问题，请按以下步骤排查：

1. **检查网络连接**: 确保可以正常访问pzds.com网站
2. **查看控制台日志**:
   - 右键点击扩展程序图标 → 检查弹出式窗口
   - 打开开发者工具查看Console标签页的错误信息
3. **重新加载扩展程序**: 在Chrome扩展程序管理页面点击"重新加载"
4. **使用搜索功能**: 尝试在游戏筛选框中输入关键词搜索

### 常见问题

- **CORS错误**: 这是正常的，扩展程序有特殊权限可以跨域访问
- **网络超时**: 可能是网络不稳定，稍后重试
- **API结构变化**: 如果pzds.com更新了API，可能需要更新代码

## 注意事项

1. 扩展程序需要访问pzds.com域名的权限
2. 监控过程中会创建隐藏的标签页来抓取数据，这是正常行为
3. 通知历史记录只保存7天，超过时间的记录会自动清理
4. 建议设置合理的检查间隔，避免对目标网站造成过大压力

## 开发说明

本项目使用纯JavaScript开发，无需额外的构建工具。如需修改代码：

1. 修改相应的文件
2. 在Chrome扩展程序管理页面点击"重新加载"按钮
3. 测试修改后的功能

## 许可证

本项目仅供学习和研究使用。
