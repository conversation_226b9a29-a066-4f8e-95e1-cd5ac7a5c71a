// 账号监控 - Content Script
// 用于抓取pzds.com商品列表页面的数据

class GoodsExtractor {
  constructor() {
    this.init();
  }

  init() {
    // 监听来自background script的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'extractGoods') {
        this.extractGoodsData().then(data => {
          sendResponse({ success: true, data: data });
        }).catch(error => {
          sendResponse({ success: false, error: error.message });
        });
        return true; // 保持消息通道开放
      }
    });

    // 页面加载完成后自动提取数据（如果需要）
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.waitForContent();
      });
    } else {
      this.waitForContent();
    }
  }

  async waitForContent() {
    // 等待商品列表加载完成
    let attempts = 0;
    const maxAttempts = 30; // 最多等待30秒

    while (attempts < maxAttempts) {
      const goodsElements = this.findGoodsElements();
      if (goodsElements.length > 0) {
        break;
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
      attempts++;
    }
  }

  findGoodsElements() {
    // 尝试多种可能的商品元素选择器
    const selectors = [
      '.goods-item',
      '.product-item',
      '.item',
      '[class*="goods"]',
      '[class*="product"]',
      '[class*="card"]',
      '.list-item',
      '.commodity-item',
      '.account-item'
    ];

    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        return Array.from(elements);
      }
    }

    // 如果没有找到，尝试查找包含价格信息的元素
    const priceElements = document.querySelectorAll('[class*="price"], .price');
    if (priceElements.length > 0) {
      return Array.from(priceElements).map(el => el.closest('div, li, article, section')).filter(Boolean);
    }

    return [];
  }

  async extractGoodsData() {
    const goods = [];
    const goodsElements = this.findGoodsElements();

    console.log(`Found ${goodsElements.length} potential goods elements`);

    for (const element of goodsElements) {
      try {
        const goodsData = this.extractSingleGoods(element);
        if (goodsData && goodsData.title && goodsData.price > 0) {
          goods.push(goodsData);
        }
      } catch (error) {
        console.error('Error extracting single goods:', error);
      }
    }

    console.log(`Extracted ${goods.length} valid goods`);
    return goods;
  }

  extractSingleGoods(element) {
    // 提取标题
    const title = this.extractTitle(element);
    
    // 提取价格
    const price = this.extractPrice(element);
    
    // 提取图片
    const image = this.extractImage(element);
    
    // 提取发布时间
    const onStandTime = this.extractTime(element);
    
    // 提取热度
    const hot = this.extractHot(element);
    
    // 提取是否支持包赔
    const hasGuarantee = this.extractGuarantee(element);
    
    // 提取链接
    const url = this.extractUrl(element);
    
    // 生成唯一ID
    const id = this.generateId(element, title, price);

    return {
      id,
      title,
      price,
      image,
      onStandTime,
      hot,
      hasGuarantee,
      url
    };
  }

  extractTitle(element) {
    const titleSelectors = [
      '.title',
      '.name',
      '.goods-name',
      '.product-name',
      '.account-title',
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      '[class*="title"]',
      '[class*="name"]'
    ];

    for (const selector of titleSelectors) {
      const titleElement = element.querySelector(selector);
      if (titleElement && titleElement.textContent.trim()) {
        return titleElement.textContent.trim();
      }
    }

    // 如果没有找到标题，尝试从元素的文本内容中提取
    const textContent = element.textContent.trim();
    if (textContent.length > 0 && textContent.length < 200) {
      return textContent.split('\n')[0].trim();
    }

    return '';
  }

  extractPrice(element) {
    const priceSelectors = [
      '.price',
      '.cost',
      '.amount',
      '[class*="price"]',
      '[class*="cost"]',
      '[class*="money"]',
      '[class*="yuan"]'
    ];

    for (const selector of priceSelectors) {
      const priceElement = element.querySelector(selector);
      if (priceElement) {
        const priceText = priceElement.textContent.trim();
        const priceMatch = priceText.match(/[\d,]+\.?\d*/);
        if (priceMatch) {
          return parseFloat(priceMatch[0].replace(/,/g, ''));
        }
      }
    }

    // 从整个元素文本中查找价格
    const allText = element.textContent;
    const pricePatterns = [
      /¥\s*([\d,]+\.?\d*)/,
      /(\d+\.?\d*)\s*元/,
      /价格[：:]\s*([\d,]+\.?\d*)/,
      /售价[：:]\s*([\d,]+\.?\d*)/
    ];

    for (const pattern of pricePatterns) {
      const match = allText.match(pattern);
      if (match) {
        return parseFloat(match[1].replace(/,/g, ''));
      }
    }

    return 0;
  }

  extractImage(element) {
    const img = element.querySelector('img');
    if (img && img.src) {
      return img.src.startsWith('http') ? img.src : new URL(img.src, window.location.origin).href;
    }
    return '';
  }

  extractTime(element) {
    const timeSelectors = [
      '.time',
      '.date',
      '.publish-time',
      '.create-time',
      '[class*="time"]',
      '[class*="date"]'
    ];

    for (const selector of timeSelectors) {
      const timeElement = element.querySelector(selector);
      if (timeElement && timeElement.textContent.trim()) {
        return timeElement.textContent.trim();
      }
    }

    // 查找时间模式
    const timePatterns = [
      /\d{4}-\d{2}-\d{2}[\s\T]\d{2}:\d{2}:\d{2}/,
      /\d{4}-\d{2}-\d{2}/,
      /\d{2}:\d{2}:\d{2}/,
      /\d+分钟前/,
      /\d+小时前/,
      /\d+天前/
    ];

    const allText = element.textContent;
    for (const pattern of timePatterns) {
      const match = allText.match(pattern);
      if (match) {
        return match[0];
      }
    }

    return new Date().toISOString();
  }

  extractHot(element) {
    const hotSelectors = [
      '.hot',
      '.popular',
      '.heat',
      '.view-count',
      '[class*="hot"]',
      '[class*="popular"]',
      '[class*="heat"]'
    ];

    for (const selector of hotSelectors) {
      const hotElement = element.querySelector(selector);
      if (hotElement && hotElement.textContent.trim()) {
        const hotText = hotElement.textContent.trim();
        const hotMatch = hotText.match(/\d+/);
        return hotMatch ? hotMatch[0] : hotText;
      }
    }

    return '0';
  }

  extractGuarantee(element) {
    const guaranteeKeywords = ['包赔', '保赔', '担保', '保证', 'guarantee'];
    const allText = element.textContent.toLowerCase();
    
    return guaranteeKeywords.some(keyword => 
      allText.includes(keyword.toLowerCase())
    );
  }

  extractUrl(element) {
    const link = element.querySelector('a');
    if (link && link.href) {
      return link.href.startsWith('http') ? link.href : new URL(link.href, window.location.origin).href;
    }
    
    // 如果没有找到链接，返回当前页面URL
    return window.location.href;
  }

  generateId(element, title, price) {
    // 尝试从元素获取data-id
    const dataId = element.getAttribute('data-id') || 
                   element.getAttribute('id') ||
                   element.getAttribute('data-goods-id') ||
                   element.getAttribute('data-product-id');
    
    if (dataId) {
      return dataId;
    }

    // 基于标题和价格生成ID
    const content = `${title}_${price}_${window.location.pathname}`;
    return this.hashCode(content).toString();
  }

  hashCode(str) {
    let hash = 0;
    if (str.length === 0) return hash;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash);
  }
}

// 初始化商品提取器
const extractor = new GoodsExtractor();
