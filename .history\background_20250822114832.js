// 账号监控 - Background Script
// 处理游戏类型API调用、定时监控逻辑和通知系统

class AccountMonitor {
  constructor() {
    this.isMonitoring = false;
    this.monitoringGames = new Set();
    this.lastKnownGoods = new Map(); // gameId -> goods array
    this.init();
  }

  async init() {
    // 监听扩展安装
    chrome.runtime.onInstalled.addListener(() => {
      this.initializeStorage();
    });

    // 监听扩展启动
    chrome.runtime.onStartup.addListener(() => {
      this.cleanupOldData();
    });

    // 监听定时器
    chrome.alarms.onAlarm.addListener((alarm) => {
      if (alarm.name === 'goodsMonitor') {
        this.checkAllGames();
      } else if (alarm.name === 'dataCleanup') {
        this.cleanupOldData();
      }
    });

    // 监听通知点击
    chrome.notifications.onClicked.addListener((notificationId) => {
      this.handleNotificationClick(notificationId);
    });

    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // 保持消息通道开放
    });

    // 设置每日数据清理定时器
    chrome.alarms.create('dataCleanup', {
      delayInMinutes: 60, // 1小时后开始
      periodInMinutes: 24 * 60 // 每24小时执行一次
    });

    // 初始化时清理一次旧数据
    this.cleanupOldData();
  }

  async initializeStorage() {
    const defaultSettings = {
      monitoringGames: [],
      priceRange: { min: 0, max: 1000 },
      checkInterval: 60, // 秒
      isMonitoring: false,
      notifications: []
    };

    const result = await chrome.storage.local.get(Object.keys(defaultSettings));
    const toSet = {};
    
    for (const [key, defaultValue] of Object.entries(defaultSettings)) {
      if (result[key] === undefined) {
        toSet[key] = defaultValue;
      }
    }

    if (Object.keys(toSet).length > 0) {
      await chrome.storage.local.set(toSet);
    }
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'getGames':
          const games = await this.fetchGames();
          sendResponse({ success: true, data: games });
          break;

        case 'startMonitoring':
          await this.startMonitoring(request.games, request.priceRange, request.interval);
          sendResponse({ success: true });
          break;

        case 'stopMonitoring':
          await this.stopMonitoring();
          sendResponse({ success: true });
          break;

        case 'getStatus':
          const status = await this.getMonitoringStatus();
          sendResponse({ success: true, data: status });
          break;

        case 'getNotifications':
          const notifications = await this.getNotifications();
          sendResponse({ success: true, data: notifications });
          break;

        case 'clearNotifications':
          await this.clearNotifications();
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Background script error:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async fetchGames() {
    try {
      const response = await fetch('https://api.pzds.com/api/web-client/v2/homepage/public/game/all', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.error('Failed to fetch games:', error);
      throw error;
    }
  }

  async startMonitoring(games, priceRange, interval) {
    this.isMonitoring = true;
    this.monitoringGames = new Set(games);

    // 保存设置
    await chrome.storage.local.set({
      monitoringGames: games,
      priceRange: priceRange,
      checkInterval: interval,
      isMonitoring: true
    });

    // 设置定时器
    chrome.alarms.clear('goodsMonitor');
    chrome.alarms.create('goodsMonitor', { 
      delayInMinutes: interval / 60,
      periodInMinutes: interval / 60
    });

    // 立即执行一次检查
    this.checkAllGames();
  }

  async stopMonitoring() {
    this.isMonitoring = false;
    this.monitoringGames.clear();

    await chrome.storage.local.set({ isMonitoring: false });
    chrome.alarms.clear('goodsMonitor');
  }

  async checkAllGames() {
    if (!this.isMonitoring) return;

    const settings = await chrome.storage.local.get(['monitoringGames', 'priceRange']);
    
    for (const gameId of settings.monitoringGames || []) {
      await this.checkGameGoods(gameId, settings.priceRange);
    }
  }

  async checkGameGoods(gameId, priceRange) {
    let tab = null;
    try {
      // 创建新标签页并注入脚本来抓取数据
      tab = await chrome.tabs.create({
        url: `https://www.pzds.com/goodsList/${gameId}/6`,
        active: false
      });

      // 等待页面加载完成
      await this.waitForPageLoad(tab.id);

      // 注入脚本获取商品数据
      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        function: this.extractGoodsData
      });

      if (results && results[0] && results[0].result) {
        const currentGoods = results[0].result;
        console.log(`Found ${currentGoods.length} goods for game ${gameId}`);
        await this.compareAndNotify(gameId, currentGoods, priceRange);
      } else {
        console.warn(`No goods data found for game ${gameId}`);
      }
    } catch (error) {
      console.error(`Error checking goods for game ${gameId}:`, error);
    } finally {
      // 确保标签页被关闭
      if (tab && tab.id) {
        try {
          await chrome.tabs.remove(tab.id);
        } catch (closeError) {
          console.warn(`Failed to close tab ${tab.id}:`, closeError);
        }
      }
    }
  }

  async waitForPageLoad(tabId, maxWaitTime = 10000) {
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const results = await chrome.scripting.executeScript({
          target: { tabId: tabId },
          function: () => document.readyState === 'complete'
        });

        if (results && results[0] && results[0].result) {
          // 页面加载完成，再等待一点时间让动态内容加载
          await new Promise(resolve => setTimeout(resolve, 2000));
          return;
        }
      } catch (error) {
        // 标签页可能还没准备好，继续等待
      }

      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.warn(`Page load timeout for tab ${tabId}`);
  }

  // 在页面中执行的函数，用于提取商品数据
  extractGoodsData() {
    const goods = [];
    
    // 这里需要根据实际页面结构来抓取数据
    // 假设商品列表在某个容器中
    const goodsElements = document.querySelectorAll('.goods-item, .product-item, [class*="goods"], [class*="product"]');
    
    goodsElements.forEach(element => {
      try {
        const titleElement = element.querySelector('[class*="title"], [class*="name"], h3, h4');
        const priceElement = element.querySelector('[class*="price"], .price');
        const imageElement = element.querySelector('img');
        const timeElement = element.querySelector('[class*="time"], [class*="date"]');
        const hotElement = element.querySelector('[class*="hot"], [class*="popular"]');
        const guaranteeElement = element.querySelector('[class*="guarantee"], [class*="保赔"]');

        if (titleElement && priceElement) {
          goods.push({
            title: titleElement.textContent?.trim() || '',
            price: parseFloat(priceElement.textContent?.replace(/[^\d.]/g, '') || '0'),
            image: imageElement?.src || '',
            onStandTime: timeElement?.textContent?.trim() || new Date().toISOString(),
            hot: hotElement?.textContent?.trim() || '0',
            hasGuarantee: !!guaranteeElement,
            url: element.querySelector('a')?.href || window.location.href,
            id: element.getAttribute('data-id') || Math.random().toString(36).substr(2, 9)
          });
        }
      } catch (error) {
        console.error('Error extracting goods data:', error);
      }
    });

    return goods;
  }

  async compareAndNotify(gameId, currentGoods, priceRange) {
    const lastGoods = this.lastKnownGoods.get(gameId) || [];
    const lastGoodsIds = new Set(lastGoods.map(g => g.id));
    
    // 找出新商品
    const newGoods = currentGoods.filter(goods => {
      return !lastGoodsIds.has(goods.id) && 
             goods.price >= priceRange.min && 
             goods.price <= priceRange.max;
    });

    // 发送通知
    for (const goods of newGoods) {
      await this.sendNotification(goods, gameId);
    }

    // 更新已知商品列表
    this.lastKnownGoods.set(gameId, currentGoods);
  }

  async sendNotification(goods, gameId) {
    const notificationId = `goods_${goods.id}_${Date.now()}`;
    
    await chrome.notifications.create(notificationId, {
      type: 'basic',
      title: '发现新商品！',
      message: `${goods.title}\n价格：¥${goods.price}\n热度：${goods.hot}${goods.hasGuarantee ? ' [支持包赔]' : ''}`,
      buttons: [{ title: '查看详情' }]
    });

    // 保存通知记录
    await this.saveNotification({
      id: notificationId,
      goods: goods,
      gameId: gameId,
      timestamp: Date.now()
    });
  }

  async saveNotification(notification) {
    try {
      const result = await chrome.storage.local.get(['notifications']);
      const notifications = result.notifications || [];

      notifications.unshift(notification);

      // 只保留7天内的通知，最多保存100条
      const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
      const filteredNotifications = notifications
        .filter(n => n.timestamp > sevenDaysAgo)
        .slice(0, 100);

      await chrome.storage.local.set({ notifications: filteredNotifications });

      // 更新通知计数
      await this.updateNotificationCount(filteredNotifications.length);
    } catch (error) {
      console.error('Failed to save notification:', error);
    }
  }

  async updateNotificationCount(count) {
    try {
      // 更新扩展程序图标上的徽章
      await chrome.action.setBadgeText({
        text: count > 0 ? count.toString() : ''
      });

      await chrome.action.setBadgeBackgroundColor({
        color: '#ff6b6b'
      });
    } catch (error) {
      console.error('Failed to update badge:', error);
    }
  }

  async cleanupOldData() {
    try {
      const result = await chrome.storage.local.get(['notifications']);
      const notifications = result.notifications || [];

      // 清理7天前的通知
      const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
      const filteredNotifications = notifications.filter(n => n.timestamp > sevenDaysAgo);

      if (filteredNotifications.length !== notifications.length) {
        await chrome.storage.local.set({ notifications: filteredNotifications });
        console.log(`Cleaned up ${notifications.length - filteredNotifications.length} old notifications`);
      }
    } catch (error) {
      console.error('Failed to cleanup old data:', error);
    }
  }

  async handleNotificationClick(notificationId) {
    const result = await chrome.storage.local.get(['notifications']);
    const notifications = result.notifications || [];
    const notification = notifications.find(n => n.id === notificationId);
    
    if (notification && notification.goods.url) {
      await chrome.tabs.create({ url: notification.goods.url });
    }
    
    chrome.notifications.clear(notificationId);
  }

  async getMonitoringStatus() {
    const result = await chrome.storage.local.get(['isMonitoring', 'monitoringGames', 'priceRange', 'checkInterval']);
    return {
      isMonitoring: result.isMonitoring || false,
      monitoringGames: result.monitoringGames || [],
      priceRange: result.priceRange || { min: 0, max: 1000 },
      checkInterval: result.checkInterval || 60
    };
  }

  async getNotifications() {
    const result = await chrome.storage.local.get(['notifications']);
    return result.notifications || [];
  }

  async clearNotifications() {
    await chrome.storage.local.set({ notifications: [] });
  }
}

// 初始化监控器
const monitor = new AccountMonitor();
