<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号监控</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>账号监控</h1>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span class="status-text">未监控</span>
            </div>
        </header>

        <div class="content">
            <!-- 游戏选择区域 -->
            <section class="section">
                <h3>选择监控游戏</h3>
                <div class="games-container" id="gamesContainer">
                    <div class="loading">正在加载游戏列表...</div>
                </div>
            </section>

            <!-- 价格设置区域 -->
            <section class="section">
                <h3>价格区间设置</h3>
                <div class="price-range">
                    <div class="input-group">
                        <label for="minPrice">最低价格</label>
                        <input type="number" id="minPrice" min="0" step="0.01" placeholder="0">
                        <span class="unit">元</span>
                    </div>
                    <div class="input-group">
                        <label for="maxPrice">最高价格</label>
                        <input type="number" id="maxPrice" min="0" step="0.01" placeholder="1000">
                        <span class="unit">元</span>
                    </div>
                </div>
            </section>

            <!-- 检查间隔设置 -->
            <section class="section">
                <h3>检查间隔</h3>
                <div class="interval-options">
                    <label class="radio-option">
                        <input type="radio" name="interval" value="30">
                        <span>30秒</span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="interval" value="60" checked>
                        <span>1分钟</span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="interval" value="300">
                        <span>5分钟</span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="interval" value="600">
                        <span>10分钟</span>
                    </label>
                </div>
            </section>

            <!-- 控制按钮 -->
            <section class="section">
                <div class="controls">
                    <button id="startBtn" class="btn btn-primary">开始监控</button>
                    <button id="stopBtn" class="btn btn-secondary" style="display: none;">停止监控</button>
                </div>
            </section>

            <!-- 通知历史 -->
            <section class="section">
                <div class="section-header">
                    <h3>通知历史</h3>
                    <button id="clearNotificationsBtn" class="btn btn-small">清空</button>
                </div>
                <div class="notifications-container" id="notificationsContainer">
                    <div class="empty-state">暂无通知记录</div>
                </div>
            </section>
        </div>

        <footer class="footer">
            <div class="stats" id="stats">
                <span>监控游戏: <strong id="gameCount">0</strong></span>
                <span>通知数量: <strong id="notificationCount">0</strong></span>
            </div>
        </footer>
    </div>

    <script src="popup.js"></script>
</body>
</html>
