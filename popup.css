/* 账号监控 - Popup样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f5f5f5;
}

.container {
    width: 380px;
    max-height: 600px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ff6b6b;
    transition: background-color 0.3s;
}

.status-dot.active {
    background: #51cf66;
}

.content {
    padding: 20px;
    max-height: 480px;
    overflow-y: auto;
}

.section {
    margin-bottom: 24px;
}

.section:last-child {
    margin-bottom: 0;
}

.section h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #495057;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.games-container {
    max-height: 120px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px;
}

.game-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.game-item:hover {
    background: #f8f9fa;
}

.game-item input[type="checkbox"] {
    margin-right: 8px;
}

.game-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    margin-right: 8px;
    object-fit: cover;
}

.game-name {
    flex: 1;
    font-size: 13px;
}

.loading {
    text-align: center;
    padding: 20px;
    color: #6c757d;
    font-size: 13px;
}

.price-range {
    display: flex;
    gap: 12px;
}

.input-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.input-group label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.input-group input {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 13px;
    transition: border-color 0.2s;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.unit {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    color: #6c757d;
    pointer-events: none;
}

.input-group {
    position: relative;
}

.interval-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.radio-option {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 13px;
}

.radio-option:hover {
    border-color: #667eea;
    background: #f8f9ff;
}

.radio-option input[type="radio"] {
    margin-right: 6px;
}

.radio-option input[type="radio"]:checked + span {
    color: #667eea;
    font-weight: 500;
}

.controls {
    display: flex;
    gap: 12px;
}

.btn {
    flex: 1;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-small {
    padding: 4px 8px;
    font-size: 11px;
    background: #e9ecef;
    color: #6c757d;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-small:hover {
    background: #dee2e6;
}

.notifications-container {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 6px;
}

.notification-item {
    padding: 12px;
    border-bottom: 1px solid #f1f3f4;
    cursor: pointer;
    transition: background-color 0.2s;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item:hover {
    background: #f8f9fa;
}

.notification-title {
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 4px;
    color: #495057;
}

.notification-details {
    font-size: 11px;
    color: #6c757d;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-price {
    color: #e74c3c;
    font-weight: 500;
}

.notification-time {
    font-size: 10px;
}

.empty-state {
    padding: 20px;
    text-align: center;
    color: #6c757d;
    font-size: 12px;
}

.footer {
    background: #f8f9fa;
    padding: 12px 20px;
    border-top: 1px solid #e9ecef;
}

.stats {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    color: #6c757d;
}

.stats strong {
    color: #495057;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
