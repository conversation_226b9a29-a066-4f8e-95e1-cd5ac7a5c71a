// 账号监控 - Popup脚本
// 处理用户界面交互和与background script的通信

class PopupManager {
  constructor() {
    this.games = [];
    this.selectedGames = new Set();
    this.isMonitoring = false;
    this.init();
  }

  async init() {
    // 绑定事件监听器
    this.bindEvents();
    
    // 加载初始数据
    await this.loadGames();
    await this.loadSettings();
    await this.loadNotifications();
    await this.updateStatus();
  }

  bindEvents() {
    // 开始监控按钮
    document.getElementById('startBtn').addEventListener('click', () => {
      this.startMonitoring();
    });

    // 停止监控按钮
    document.getElementById('stopBtn').addEventListener('click', () => {
      this.stopMonitoring();
    });

    // 清空通知按钮
    document.getElementById('clearNotificationsBtn').addEventListener('click', () => {
      this.clearNotifications();
    });

    // 价格输入框变化
    document.getElementById('minPrice').addEventListener('input', () => {
      this.saveSettings();
    });

    document.getElementById('maxPrice').addEventListener('input', () => {
      this.saveSettings();
    });

    // 检查间隔变化
    document.querySelectorAll('input[name="interval"]').forEach(radio => {
      radio.addEventListener('change', () => {
        this.saveSettings();
      });
    });
  }

  async loadGames() {
    const gamesContainer = document.getElementById('gamesContainer');
    
    try {
      const response = await this.sendMessage({ action: 'getGames' });
      
      if (response.success) {
        this.games = response.data;
        this.renderGames();
      } else {
        gamesContainer.innerHTML = '<div class="loading">加载游戏列表失败</div>';
      }
    } catch (error) {
      console.error('Failed to load games:', error);
      gamesContainer.innerHTML = '<div class="loading">加载游戏列表失败</div>';
    }
  }

  renderGames() {
    const gamesContainer = document.getElementById('gamesContainer');
    
    if (this.games.length === 0) {
      gamesContainer.innerHTML = '<div class="empty-state">暂无可用游戏</div>';
      return;
    }

    const gamesHtml = this.games.map(game => `
      <div class="game-item" data-game-id="${game.id}">
        <input type="checkbox" id="game-${game.id}" ${this.selectedGames.has(game.id) ? 'checked' : ''}>
        <img src="${game.icon || ''}" alt="${game.name}" class="game-icon" style="display: ${game.icon ? 'block' : 'none'}">
        <span class="game-name">${game.name}</span>
      </div>
    `).join('');

    gamesContainer.innerHTML = gamesHtml;

    // 绑定游戏选择事件
    gamesContainer.querySelectorAll('.game-item').forEach(item => {
      const checkbox = item.querySelector('input[type="checkbox"]');
      const gameId = item.dataset.gameId;

      item.addEventListener('click', (e) => {
        if (e.target.type !== 'checkbox') {
          checkbox.checked = !checkbox.checked;
        }
        
        if (checkbox.checked) {
          this.selectedGames.add(gameId);
        } else {
          this.selectedGames.delete(gameId);
        }
        
        this.saveSettings();
        this.updateStats();
      });
    });
  }

  async loadSettings() {
    try {
      const response = await this.sendMessage({ action: 'getStatus' });
      
      if (response.success) {
        const settings = response.data;
        
        // 设置选中的游戏
        this.selectedGames = new Set(settings.monitoringGames || []);
        
        // 设置价格区间
        document.getElementById('minPrice').value = settings.priceRange?.min || 0;
        document.getElementById('maxPrice').value = settings.priceRange?.max || 1000;
        
        // 设置检查间隔
        const intervalRadio = document.querySelector(`input[name="interval"][value="${settings.checkInterval || 60}"]`);
        if (intervalRadio) {
          intervalRadio.checked = true;
        }
        
        // 设置监控状态
        this.isMonitoring = settings.isMonitoring || false;
        
        // 重新渲染游戏列表以显示选中状态
        if (this.games.length > 0) {
          this.renderGames();
        }
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  async saveSettings() {
    const settings = {
      monitoringGames: Array.from(this.selectedGames),
      priceRange: {
        min: parseFloat(document.getElementById('minPrice').value) || 0,
        max: parseFloat(document.getElementById('maxPrice').value) || 1000
      },
      checkInterval: parseInt(document.querySelector('input[name="interval"]:checked')?.value) || 60
    };

    // 保存到本地存储
    await chrome.storage.local.set(settings);
  }

  async startMonitoring() {
    if (this.selectedGames.size === 0) {
      alert('请至少选择一个游戏进行监控');
      return;
    }

    const settings = {
      games: Array.from(this.selectedGames),
      priceRange: {
        min: parseFloat(document.getElementById('minPrice').value) || 0,
        max: parseFloat(document.getElementById('maxPrice').value) || 1000
      },
      interval: parseInt(document.querySelector('input[name="interval"]:checked')?.value) || 60
    };

    try {
      const response = await this.sendMessage({
        action: 'startMonitoring',
        ...settings
      });

      if (response.success) {
        this.isMonitoring = true;
        this.updateStatus();
        this.showMessage('监控已开始', 'success');
      } else {
        this.showMessage('启动监控失败: ' + response.error, 'error');
      }
    } catch (error) {
      console.error('Failed to start monitoring:', error);
      this.showMessage('启动监控失败', 'error');
    }
  }

  async stopMonitoring() {
    try {
      const response = await this.sendMessage({ action: 'stopMonitoring' });

      if (response.success) {
        this.isMonitoring = false;
        this.updateStatus();
        this.showMessage('监控已停止', 'info');
      } else {
        this.showMessage('停止监控失败: ' + response.error, 'error');
      }
    } catch (error) {
      console.error('Failed to stop monitoring:', error);
      this.showMessage('停止监控失败', 'error');
    }
  }

  async clearNotifications() {
    try {
      const response = await this.sendMessage({ action: 'clearNotifications' });

      if (response.success) {
        await this.loadNotifications();
        this.showMessage('通知记录已清空', 'info');
      } else {
        this.showMessage('清空通知失败: ' + response.error, 'error');
      }
    } catch (error) {
      console.error('Failed to clear notifications:', error);
      this.showMessage('清空通知失败', 'error');
    }
  }

  async loadNotifications() {
    try {
      const response = await this.sendMessage({ action: 'getNotifications' });
      
      if (response.success) {
        this.renderNotifications(response.data);
        this.updateStats();
      }
    } catch (error) {
      console.error('Failed to load notifications:', error);
    }
  }

  renderNotifications(notifications) {
    const container = document.getElementById('notificationsContainer');
    
    if (notifications.length === 0) {
      container.innerHTML = '<div class="empty-state">暂无通知记录</div>';
      return;
    }

    const notificationsHtml = notifications.slice(0, 10).map(notification => {
      const goods = notification.goods;
      const time = new Date(notification.timestamp).toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });

      return `
        <div class="notification-item" data-url="${goods.url}">
          <div class="notification-title">${goods.title}</div>
          <div class="notification-details">
            <span class="notification-price">¥${goods.price}</span>
            <span class="notification-time">${time}</span>
          </div>
        </div>
      `;
    }).join('');

    container.innerHTML = notificationsHtml;

    // 绑定点击事件
    container.querySelectorAll('.notification-item').forEach(item => {
      item.addEventListener('click', () => {
        const url = item.dataset.url;
        if (url) {
          chrome.tabs.create({ url: url });
        }
      });
    });
  }

  updateStatus() {
    const statusIndicator = document.getElementById('statusIndicator');
    const statusDot = statusIndicator.querySelector('.status-dot');
    const statusText = statusIndicator.querySelector('.status-text');
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');

    if (this.isMonitoring) {
      statusDot.classList.add('active');
      statusText.textContent = '监控中';
      startBtn.style.display = 'none';
      stopBtn.style.display = 'block';
    } else {
      statusDot.classList.remove('active');
      statusText.textContent = '未监控';
      startBtn.style.display = 'block';
      stopBtn.style.display = 'none';
    }
  }

  updateStats() {
    document.getElementById('gameCount').textContent = this.selectedGames.size;

    // 更新通知计数
    this.sendMessage({ action: 'getNotifications' }).then(response => {
      if (response.success) {
        document.getElementById('notificationCount').textContent = response.data.length;
      }
    });
  }

  showMessage(message, type = 'info') {
    // 简单的消息提示，可以根据需要改进
    console.log(`[${type.toUpperCase()}] ${message}`);
  }

  async sendMessage(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, resolve);
    });
  }
}

// 初始化Popup管理器
document.addEventListener('DOMContentLoaded', () => {
  new PopupManager();
});
